<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云寄售 - 消费者购物平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 30px;
        }

        .welcome-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .merchant-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #667eea;
        }

        .merchant-info h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.8em;
        }

        .merchant-info p {
            color: #666;
            line-height: 1.6;
            font-size: 1.1em;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .product-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .product-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
        }

        .product-card.out-of-stock {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .product-card.out-of-stock:hover {
            transform: none;
            border-color: #e9ecef;
        }

        .product-name {
            font-size: 1.4em;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .product-price {
            font-size: 1.6em;
            color: #667eea;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .product-description {
            color: #666;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .product-stock {
            font-size: 0.9em;
            color: #28a745;
            margin-bottom: 15px;
        }

        .product-stock.out-of-stock {
            color: #dc3545;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-success {
            background: #28a745;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            position: relative;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            right: 20px;
            top: 15px;
        }

        .close:hover {
            color: #000;
        }

        .payment-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .order-info {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .products-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛒 云寄售商城</h1>
            <p>安全便捷的在线购物平台</p>
        </div>
        
        <div class="content">
            <div id="loading" class="loading">
                <h3>🔄 正在加载商户信息...</h3>
            </div>
            
            <div id="error-container"></div>
            
            <div id="merchant-section" style="display: none;">
                <div class="merchant-info">
                    <h2 id="shop-name">商店名称</h2>
                    <p id="shop-description">商店介绍</p>
                </div>
                
                <div id="products-container">
                    <h3 style="margin-bottom: 20px; color: #333;">📦 商品列表</h3>
                    <div id="products-grid" class="products-grid"></div>
                </div>
            </div>
            
            <div id="product-section" style="display: none;">
                <div id="product-detail"></div>
            </div>
        </div>
    </div>

    <!-- 购买确认模态框 -->
    <div id="buyModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>💳 选择支付方式</h2>
            <div id="product-summary"></div>
            <div class="payment-buttons">
                <button class="btn" onclick="createOrder('wxpay')">微信支付</button>
                <button class="btn" onclick="createOrder('alipay')">支付宝</button>
            </div>
        </div>
    </div>

    <!-- 订单详情模态框 -->
    <div id="orderModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>🛒 订单详情</h2>
            <div id="order-detail"></div>
        </div>
    </div>

    <script>
        // 配置
        const API_BASE_URL = 'http://127.0.0.1:7893/';
        
        // 全局变量
        let currentMerchantId = null;
        let currentProductId = null;
        let currentOrderId = null;

        // 获取URL参数
        function getUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const merchantId = urlParams.get('merchant_id');
            const productId = urlParams.get('product_id');
            return { merchantId, productId };
        }

        // 商户秘钥生成算法（与bot.py保持一致）
        function generateMerchantSecret(merchantId) {
            // 注意：在实际生产环境中，这个算法应该在后端实现
            // 这里仅为演示目的
            return `${merchantId.substring(0, 3)}_generated_secret`;
        }

        // API调用函数
        async function callApi(endpoint, params = {}) {
            try {
                const url = new URL(endpoint, API_BASE_URL);
                Object.keys(params).forEach(key => {
                    if (params[key] !== null && params[key] !== undefined) {
                        url.searchParams.append(key, params[key]);
                    }
                });
                
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error('API调用失败:', error);
                return null;
            }
        }

        // 显示错误信息
        function showError(message) {
            const errorContainer = document.getElementById('error-container');
            errorContainer.innerHTML = `<div class="error">❌ ${message}</div>`;
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        // 获取商户信息
        async function getMerchantInfo(merchantId) {
            const response = await callApi('get_merchant_info.php', { merchant_id: merchantId });
            return response;
        }

        // 获取商品列表
        async function getProductList(merchantSecret) {
            const response = await callApi('get_product_list.php', { merchant_secret: merchantSecret });
            return response;
        }

        // 获取商品信息
        async function getProductInfo(productId) {
            const response = await callApi('get_product_info.php', { product_id: productId });
            return response;
        }

        // 创建订单
        async function createOrderApi(customerContact, productId, payType = 'wxpay') {
            const response = await callApi('create_order.php', {
                customer_contact: customerContact,
                product_id: productId,
                pay_type: payType
            });
            return response;
        }

        // 检查支付状态
        async function checkPaymentStatus(orderId) {
            const response = await callApi('check_payment_status.php', { order_id: orderId });
            return response;
        }

        // 初始化页面
        async function initPage() {
            const { merchantId, productId } = getUrlParams();

            if (productId) {
                // 直接显示商品详情
                await showProductDetail(productId);
            } else if (merchantId) {
                // 显示商户商店
                await showMerchantShop(merchantId);
            } else {
                hideLoading();
                showError('缺少必要参数：merchant_id 或 product_id');
            }
        }

        // 显示商户商店
        async function showMerchantShop(merchantId) {
            currentMerchantId = merchantId;

            try {
                // 获取商户信息
                const merchantResponse = await getMerchantInfo(merchantId);
                if (!merchantResponse || merchantResponse.status !== 'success') {
                    throw new Error('商户不存在或获取商户信息失败');
                }

                const merchantInfo = merchantResponse.data;

                // 显示商户信息
                document.getElementById('shop-name').textContent = merchantInfo.shop_name || '未知商店';
                document.getElementById('shop-description').textContent = merchantInfo.shop_description || '暂无介绍';

                // 获取商品列表
                const merchantSecret = generateMerchantSecret(merchantId);
                const productsResponse = await getProductList(merchantSecret);

                let products = [];
                if (productsResponse && productsResponse.status === 'success') {
                    products = productsResponse.data || [];
                }

                // 显示商品列表
                displayProducts(products);

                // 显示商户区域
                hideLoading();
                document.getElementById('merchant-section').style.display = 'block';

            } catch (error) {
                hideLoading();
                showError(error.message);
            }
        }

        // 显示商品详情
        async function showProductDetail(productId) {
            currentProductId = productId;

            try {
                const productResponse = await getProductInfo(productId);
                if (!productResponse || productResponse.status !== 'success') {
                    throw new Error('获取商品信息失败');
                }

                const productInfo = productResponse.data;
                currentMerchantId = productInfo.merchant_id;

                // 创建商品详情HTML
                const productDetailHtml = `
                    <div class="product-card" style="max-width: 600px; margin: 0 auto;">
                        <h2 class="product-name">📦 ${productInfo.product_name}</h2>
                        <div class="product-price">💰 ¥${productInfo.product_price}</div>
                        <div class="product-description">${productInfo.product_description || '暂无描述'}</div>
                        <div class="product-stock ${parseInt(productInfo.stock_quantity) <= 0 ? 'out-of-stock' : ''}">
                            📊 库存：${productInfo.stock_quantity} 件
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            ${parseInt(productInfo.stock_quantity) > 0 ?
                                `<button class="btn" onclick="showBuyModal('${productId}', '${productInfo.product_name}', '${productInfo.product_price}')">🛒 立即购买</button>` :
                                `<button class="btn" disabled>❌ 商品已售罄</button>`
                            }
                            <button class="btn btn-secondary" onclick="shareProduct('${productId}')">📤 分享商品</button>
                        </div>
                    </div>
                `;

                document.getElementById('product-detail').innerHTML = productDetailHtml;

                hideLoading();
                document.getElementById('product-section').style.display = 'block';

            } catch (error) {
                hideLoading();
                showError(error.message);
            }
        }

        // 显示商品列表
        function displayProducts(products) {
            const productsGrid = document.getElementById('products-grid');

            if (!products || products.length === 0) {
                productsGrid.innerHTML = '<div class="product-card"><h3>⚠️ 该商户还没有商品</h3></div>';
                return;
            }

            const productsHtml = products.map(product => {
                const stockQuantity = parseInt(product.stock_quantity) || 0;
                const isOutOfStock = stockQuantity <= 0;

                return `
                    <div class="product-card ${isOutOfStock ? 'out-of-stock' : ''}"
                         onclick="${isOutOfStock ? '' : `showBuyModal('${product.product_id}', '${product.product_name}', '${product.product_price}')`}">
                        <div class="product-name">${product.product_name}</div>
                        <div class="product-price">¥${product.product_price}</div>
                        <div class="product-description">${product.product_description || '暂无描述'}</div>
                        <div class="product-stock ${isOutOfStock ? 'out-of-stock' : ''}">
                            ${isOutOfStock ? '❌ 已售罄' : `✅ 库存：${stockQuantity} 件`}
                        </div>
                        <div style="text-align: center; margin-top: 15px;">
                            ${isOutOfStock ?
                                '<button class="btn" disabled>❌ 已售罄</button>' :
                                '<button class="btn">🛒 立即购买</button>'
                            }
                        </div>
                    </div>
                `;
            }).join('');

            productsGrid.innerHTML = productsHtml;
        }

        // 显示购买模态框
        function showBuyModal(productId, productName, productPrice) {
            currentProductId = productId;

            const productSummary = document.getElementById('product-summary');
            productSummary.innerHTML = `
                <div class="order-info">
                    <h3>📦 ${productName}</h3>
                    <p><strong>💰 价格：</strong>¥${productPrice}</p>
                    <p><strong>🏪 商户ID：</strong>${currentMerchantId}</p>
                </div>
            `;

            document.getElementById('buyModal').style.display = 'block';
        }

        // 创建订单
        async function createOrder(payType) {
            if (!currentProductId) {
                alert('❌ 商品信息错误');
                return;
            }

            // 生成客户联系方式（使用时间戳作为临时ID）
            const customerContact = `customer_${Date.now()}`;

            try {
                const orderResponse = await createOrderApi(customerContact, currentProductId, payType);
                if (!orderResponse || orderResponse.status !== 'success') {
                    throw new Error(orderResponse?.message || '创建订单失败');
                }

                const orderData = orderResponse.data;
                const orderInfo = orderData.order_info;
                const paymentUrl = orderData.payment_url;

                currentOrderId = orderInfo.order_id;

                // 关闭购买模态框
                document.getElementById('buyModal').style.display = 'none';

                // 显示订单详情
                showOrderDetail(orderInfo, paymentUrl);

            } catch (error) {
                alert(`❌ ${error.message}`);
            }
        }

        // 显示订单详情
        function showOrderDetail(orderInfo, paymentUrl) {
            const orderDetail = document.getElementById('order-detail');
            orderDetail.innerHTML = `
                <div class="order-info">
                    <h3>🛒 订单创建成功！</h3>
                    <p><strong>📋 订单号：</strong>${orderInfo.order_id}</p>
                    <p><strong>🏪 商户ID：</strong>${orderInfo.merchant_id}</p>
                    <p><strong>📦 商品名称：</strong>${orderInfo.product_name}</p>
                    <p><strong>💰 需支付金额：</strong>¥${orderInfo.product_price}</p>
                    <p><strong>👤 购买人ID：</strong>${orderInfo.customer_contact}</p>
                    <p><strong>⏰ 订单创建时间：</strong>${orderInfo.created_at}</p>
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <a href="${paymentUrl}" target="_blank" class="btn">💳 立即支付</a>
                    <button class="btn btn-success" onclick="checkPayment('${orderInfo.order_id}')">✅ 我已支付</button>
                </div>
            `;

            document.getElementById('orderModal').style.display = 'block';
        }

        // 检查支付状态
        async function checkPayment(orderId) {
            try {
                const paymentResponse = await checkPaymentStatus(orderId);
                if (!paymentResponse || paymentResponse.status !== 'success') {
                    alert('❌ 检查支付状态失败');
                    return;
                }

                const orderData = paymentResponse.data;
                const orderStatus = orderData.order_status;

                if (orderStatus === 'paid') {
                    // 支付成功，显示完整信息
                    const orderDetail = document.getElementById('order-detail');
                    orderDetail.innerHTML = `
                        <div class="order-info" style="background: #d4edda; border-color: #c3e6cb;">
                            <h3>✅ 支付成功！</h3>
                            <p><strong>📋 订单号：</strong>${orderData.order_id}</p>
                            <p><strong>🏪 商户ID：</strong>${orderData.merchant_id}</p>
                            <p><strong>📦 商品名称：</strong>${orderData.product_name}</p>
                            <p><strong>💰 商品金额：</strong>¥${orderData.product_price}</p>
                            <p><strong>⏰ 订单发起时间：</strong>${orderData.created_at}</p>
                            <p><strong>💳 支付时间：</strong>${orderData.updated_at}</p>
                            <p><strong>👤 购买人ID：</strong>${orderData.customer_contact}</p>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin: 15px 0;">
                            <h4>🎁 发货内容：</h4>
                            <pre style="background: #e9ecef; padding: 10px; border-radius: 5px; white-space: pre-wrap;">${orderData.delivery_content}</pre>
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            <a href="https://cloudshop.qnm6.top/tousu.html" target="_blank" class="btn btn-secondary">投诉此订单</a>
                            <button class="btn" onclick="closeModal('orderModal')">关闭</button>
                        </div>
                    `;
                } else {
                    // 订单未支付
                    alert('❌ 订单未支付！请完成支付后再点击"我已支付"按钮');
                }

            } catch (error) {
                alert(`❌ 检查支付状态失败: ${error.message}`);
            }
        }

        // 分享商品
        function shareProduct(productId) {
            const shareUrl = `${window.location.origin}${window.location.pathname}?product_id=${productId}`;
            const shareText = `💎 推荐好物，自动发货，欢迎下单！\n\n🔗 商品直达链接：${shareUrl}`;

            if (navigator.share) {
                navigator.share({
                    title: '云寄售商城 - 商品分享',
                    text: shareText,
                    url: shareUrl
                });
            } else {
                // 复制到剪贴板
                navigator.clipboard.writeText(shareText).then(() => {
                    alert('📤 商品推广文案已复制到剪贴板！');
                }).catch(() => {
                    // 如果复制失败，显示文本让用户手动复制
                    prompt('📤 请复制以下推广文案：', shareText);
                });
            }
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 模态框事件处理
        document.addEventListener('DOMContentLoaded', function() {
            // 关闭按钮事件
            document.querySelectorAll('.close').forEach(closeBtn => {
                closeBtn.onclick = function() {
                    this.closest('.modal').style.display = 'none';
                }
            });

            // 点击模态框外部关闭
            window.onclick = function(event) {
                if (event.target.classList.contains('modal')) {
                    event.target.style.display = 'none';
                }
            }

            // 初始化页面
            initPage();
        });
    </script>
</body>
</html>
