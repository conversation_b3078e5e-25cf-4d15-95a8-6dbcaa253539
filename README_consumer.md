# 云寄售消费者前端页面

## 概述

这是一个面向消费者的前端购物页面，模仿了 `bot.py` 中的逻辑结构，提供了完整的商品浏览和购买功能。

## 功能特性

### 🛒 核心功能
- **商户商店浏览** - 查看商户信息和商品列表
- **商品详情展示** - 详细的商品信息和库存状态
- **在线下单购买** - 支持微信支付和支付宝
- **订单状态查询** - 实时检查支付状态
- **商品分享功能** - 生成分享链接推广商品

### 📱 用户体验
- **响应式设计** - 完美适配手机和桌面设备
- **现代化UI** - 渐变色彩和流畅动画效果
- **直观操作** - 简洁明了的用户界面
- **实时反馈** - 即时的状态更新和错误提示

## 使用方法

### 访问方式

#### 1. 查看商户商店
```
consumer.html?merchant_id=商户ID
```
例如：`consumer.html?merchant_id=M001`

#### 2. 直接查看商品
```
consumer.html?product_id=商品ID
```
例如：`consumer.html?product_id=123`

### 购买流程

1. **浏览商品** - 查看商品详情、价格和库存
2. **选择支付方式** - 微信支付或支付宝
3. **创建订单** - 系统自动生成订单和支付链接
4. **完成支付** - 跳转到第三方支付平台
5. **确认支付** - 点击"我已支付"按钮
6. **获取商品** - 支付成功后自动显示发货内容

## 技术实现

### API 集成
页面集成了以下 API 接口：
- `get_merchant_info.php` - 获取商户信息
- `get_product_list.php` - 获取商品列表
- `get_product_info.php` - 获取商品详情
- `create_order.php` - 创建订单
- `check_payment_status.php` - 检查支付状态

### 核心功能模块

#### 1. 动态参数解析
```javascript
function getUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const merchantId = urlParams.get('merchant_id');
    const productId = urlParams.get('product_id');
    return { merchantId, productId };
}
```

#### 2. API 调用封装
```javascript
async function callApi(endpoint, params = {}) {
    const url = new URL(endpoint, API_BASE_URL);
    Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
            url.searchParams.append(key, params[key]);
        }
    });
    
    const response = await fetch(url);
    return await response.json();
}
```

#### 3. 商户秘钥生成
```javascript
function generateMerchantSecret(merchantId) {
    // 注意：在生产环境中应该在后端实现
    return `${merchantId.substring(0, 3)}_generated_secret`;
}
```

### 页面逻辑流程

#### 商户商店模式
1. 解析 URL 参数获取 `merchant_id`
2. 调用 `get_merchant_info.php` 获取商户信息
3. 生成商户秘钥并调用 `get_product_list.php` 获取商品列表
4. 渲染商户信息和商品网格

#### 商品详情模式
1. 解析 URL 参数获取 `product_id`
2. 调用 `get_product_info.php` 获取商品详情
3. 渲染商品详情页面
4. 提供购买和分享功能

#### 购买流程
1. 用户选择支付方式（微信/支付宝）
2. 调用 `create_order.php` 创建订单
3. 显示订单详情和支付链接
4. 用户完成支付后点击"我已支付"
5. 调用 `check_payment_status.php` 验证支付状态
6. 支付成功后显示发货内容

## 配置说明

### API 基础地址
```javascript
const API_BASE_URL = 'http://127.0.0.1:7893/';
```

### 客户联系方式生成
```javascript
const customerContact = `customer_${Date.now()}`;
```

## 安全注意事项

1. **商户秘钥生成** - 当前在前端实现仅为演示，生产环境应移至后端
2. **客户身份** - 使用时间戳生成临时客户ID，可根据需要改为用户登录系统
3. **支付验证** - 依赖第三方支付平台的回调验证

## 兼容性

- **现代浏览器** - 支持 ES6+ 语法和 Fetch API
- **移动设备** - 响应式设计，完美适配手机端
- **分享功能** - 优先使用 Web Share API，降级到剪贴板复制

## 部署建议

1. 将 `consumer.html` 部署到 Web 服务器
2. 确保 API 服务正常运行
3. 根据实际情况修改 `API_BASE_URL`
4. 测试各项功能是否正常工作

## 扩展功能

可以考虑添加的功能：
- 用户登录系统
- 购物车功能
- 订单历史查询
- 商品搜索和筛选
- 商品评价系统
- 优惠券支持

## 与 bot.py 的对应关系

| bot.py 功能 | consumer.html 对应功能 |
|------------|----------------------|
| `/start merchant_id` | `?merchant_id=xxx` |
| `/start shoping_product_id` | `?product_id=xxx` |
| 商户信息展示 | `showMerchantShop()` |
| 商品列表按钮 | 商品网格卡片 |
| 商品详情按钮 | 商品详情页面 |
| 支付方式选择 | 支付方式模态框 |
| 订单创建 | `createOrder()` |
| 支付状态检查 | `checkPayment()` |
| 分享功能 | `shareProduct()` |

这个前端页面完全模仿了 bot.py 的逻辑结构，提供了相同的功能体验，但以 Web 页面的形式呈现给用户。
