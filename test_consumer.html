<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云寄售消费者页面 - 测试入口</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 30px;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #667eea;
        }

        .test-section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .test-section p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-success {
            background: #28a745;
        }

        .example-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .example-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }

        .example-card h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .example-card p {
            color: #666;
            margin-bottom: 15px;
            font-size: 0.9em;
        }

        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin: 10px 0;
            word-break: break-all;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .example-links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 云寄售消费者页面测试</h1>
            <p>测试不同参数的页面效果</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h2>📋 使用说明</h2>
                <p>这个页面用于测试消费者前端页面的不同访问方式。你可以通过以下两种方式访问：</p>
                <ul style="margin-left: 20px; color: #666;">
                    <li><strong>商户模式：</strong> 显示商户的商店信息和所有商品列表</li>
                    <li><strong>商品模式：</strong> 直接显示特定商品的详细信息</li>
                </ul>
            </div>

            <div class="test-section">
                <h2>🛒 商户模式测试</h2>
                <p>输入商户ID来查看该商户的商店页面：</p>
                <div class="form-group">
                    <label for="merchantId">商户ID：</label>
                    <input type="text" id="merchantId" placeholder="例如：M001" value="M001">
                </div>
                <button class="btn" onclick="openMerchantPage()">🏪 打开商户商店</button>
                
                <div class="code">
                    URL格式：consumer.html?merchant_id=商户ID
                </div>
            </div>

            <div class="test-section">
                <h2>📦 商品模式测试</h2>
                <p>输入商品ID来直接查看商品详情页面：</p>
                <div class="form-group">
                    <label for="productId">商品ID：</label>
                    <input type="text" id="productId" placeholder="例如：123" value="1">
                </div>
                <button class="btn" onclick="openProductPage()">📦 打开商品详情</button>
                
                <div class="code">
                    URL格式：consumer.html?product_id=商品ID
                </div>
            </div>

            <div class="test-section">
                <h2>🔗 示例链接</h2>
                <p>以下是一些预设的测试链接：</p>
                
                <div class="example-links">
                    <div class="example-card">
                        <h3>🏪 商户 M001</h3>
                        <p>查看商户M001的商店和商品列表</p>
                        <a href="consumer.html?merchant_id=M001" class="btn" target="_blank">访问商店</a>
                    </div>
                    
                    <div class="example-card">
                        <h3>🏪 商户 M002</h3>
                        <p>查看商户M002的商店和商品列表</p>
                        <a href="consumer.html?merchant_id=M002" class="btn" target="_blank">访问商店</a>
                    </div>
                    
                    <div class="example-card">
                        <h3>📦 商品 #1</h3>
                        <p>直接查看商品ID为1的商品详情</p>
                        <a href="consumer.html?product_id=1" class="btn btn-success" target="_blank">查看商品</a>
                    </div>
                    
                    <div class="example-card">
                        <h3>📦 商品 #2</h3>
                        <p>直接查看商品ID为2的商品详情</p>
                        <a href="consumer.html?product_id=2" class="btn btn-success" target="_blank">查看商品</a>
                    </div>
                </div>
            </div>

            <div class="test-section">
                <h2>⚙️ API 配置</h2>
                <p>确保以下配置正确：</p>
                <div class="code">
                    API_BASE_URL = 'http://127.0.0.1:7893/'
                </div>
                <p style="margin-top: 10px; color: #666; font-size: 0.9em;">
                    如果API地址不同，请修改 consumer.html 中的 API_BASE_URL 配置。
                </p>
            </div>

            <div class="test-section">
                <h2>🔧 功能测试清单</h2>
                <div style="color: #666;">
                    <h4>商户模式测试：</h4>
                    <ul style="margin-left: 20px; margin-bottom: 15px;">
                        <li>✅ 商户信息显示</li>
                        <li>✅ 商品列表展示</li>
                        <li>✅ 库存状态显示</li>
                        <li>✅ 商品卡片点击</li>
                    </ul>
                    
                    <h4>商品模式测试：</h4>
                    <ul style="margin-left: 20px; margin-bottom: 15px;">
                        <li>✅ 商品详情显示</li>
                        <li>✅ 购买按钮功能</li>
                        <li>✅ 分享功能</li>
                        <li>✅ 库存检查</li>
                    </ul>
                    
                    <h4>购买流程测试：</h4>
                    <ul style="margin-left: 20px;">
                        <li>✅ 支付方式选择</li>
                        <li>✅ 订单创建</li>
                        <li>✅ 支付链接跳转</li>
                        <li>✅ 支付状态检查</li>
                        <li>✅ 发货内容显示</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openMerchantPage() {
            const merchantId = document.getElementById('merchantId').value.trim();
            if (!merchantId) {
                alert('请输入商户ID');
                return;
            }
            window.open(`consumer.html?merchant_id=${encodeURIComponent(merchantId)}`, '_blank');
        }

        function openProductPage() {
            const productId = document.getElementById('productId').value.trim();
            if (!productId) {
                alert('请输入商品ID');
                return;
            }
            window.open(`consumer.html?product_id=${encodeURIComponent(productId)}`, '_blank');
        }

        // 回车键支持
        document.getElementById('merchantId').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                openMerchantPage();
            }
        });

        document.getElementById('productId').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                openProductPage();
            }
        });
    </script>
</body>
</html>
